# Simonitor

A professional Sims 4 Package Analyzer & Mod Manager built with Electron, TypeScript, and Vue.js.

## 🎯 **Current Status: Phase 4 - ModDashboard Display Issue (CRITICAL)**

Simonitor has achieved **exceptional analysis capabilities** and **successful UI integration**, but the ModDashboard is not displaying analysis results. The analysis engine processes mods correctly and data reaches the frontend, but the filteredMods computation or template rendering is broken.

### ✅ **COMPLETED PHASES**

**Phase 3A: Advanced Intelligence System**
- ✅ **99% Resource Intelligence Coverage** (1,332/1,339 files)
- ✅ **100% Script Intelligence** for .ts4script files
- ✅ **97% Author Detection Rate** across 134 unique creators
- ✅ **Processing Speed**: 6 files/second (168ms average)
- ✅ **Quality Assessment**: 99% coverage with 78/100 average score
- ✅ **Enhanced Metadata Extraction** with confidence scoring

**Phase 4A: Advanced UI Design System**
- ✅ **Apple-inspired Design System** with Sims 4 aesthetics
- ✅ **ModDashboard Component** with filtering, search, multiple views
- ✅ **Enhanced Electron Main Process** with folder analysis
- ✅ **Complete Component Library** (ModCard, Intelligence displays, etc.)
- ✅ **IPC Handlers** for batch processing and export

### ⚠️ **CRITICAL ISSUE TO RESOLVE**
1. **ModDashboard Display Logic**: filteredMods computed property not rendering results
   - ✅ Analysis engine works (35 mods processed)
   - ✅ Data reaches ModDashboard (Props.mods: 35 confirmed)
   - ❌ Template not showing results (v-if/v-else-if logic issue)
   - **Debug Target**: ModDashboard.vue lines 309-378 (filteredMods) and template rendering

### 🎯 **IMMEDIATE NEXT STEPS**
- **Fix UI Integration**: Connect ModDashboard to main App.vue
- **Restore Analysis Flow**: Ensure analysis results reach new components
- **Test Complete Workflow**: Verify folder analysis → dashboard display
- **Validate All Features**: Confirm export, filtering, search functionality

## 🚀 **Quick Start**

### Prerequisites
- Node.js 16+ 
- npm or yarn

### Installation & Development
```bash
# Clone the repository
git clone <repository-url>
cd simonitor

# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build

# Run tests
npm test
```

## 📊 **Features**

### **Core Analysis Engine**
- **Resource Type Detection**: Identifies 20+ Sims 4 resource types with human-readable names
- **Metadata Extraction**: Detailed information about CAS parts, thumbnails, images, and more
- **Size Analysis**: Compressed and decompressed size reporting
- **Error Handling**: Graceful handling of unknown or corrupted resources

### **Professional User Interface**
- **Drag & Drop**: Intuitive file upload with visual feedback
- **Advanced Tables**: Sortable, filterable, searchable resource displays
- **Export Options**: JSON and CSV export with customization
- **Settings Panel**: Comprehensive user preferences and configuration
- **Responsive Design**: Mobile-friendly interface

### **Supported File Types**
- ✅ `.package` files (Sims 4 mod packages)
- ✅ `.ts4script` files (Python script mods)
- 🔄 Additional formats planned for future releases

## 🏗️ **Architecture**

Simonitor is built with a modular, extensible architecture:

### **Technology Stack**
- **Frontend**: Vue.js 3 + TypeScript + Custom CSS Design System
- **Backend**: Electron + Node.js + TypeScript
- **Analysis**: @s4tk/models library for Sims 4 package parsing
- **Testing**: Vitest with 50+ real mod test assets
- **Build**: Vite + electron-vite for optimized builds

### **Core Components**
- **PackageAnalysisService**: Main analysis orchestrator
- **ResourceProcessor**: Resource iteration and processing
- **ResourceMetadataExtractor**: Type-specific metadata extraction
- **Unified Resource Types (URT)**: Single source of truth for resource identification

## 📁 **Project Structure**

```
simonitor/
├── src/
│   ├── main/                 # Electron main process
│   ├── preload/              # IPC bridge
│   ├── renderer/             # Vue.js UI
│   │   ├── components/       # UI components
│   │   └── styles/           # Design system
│   ├── services/analysis/    # Core analysis engine
│   ├── types/                # TypeScript definitions
│   └── constants/            # URT and configuration
├── tests/                    # Test files and assets
├── docs/                     # Comprehensive documentation
└── out/                      # Build output
```

## 📚 **Documentation**

Comprehensive documentation is available in the `docs/` directory:

- **[Strategic Vision](docs/planning/STRATEGIC_VISION.md)** - Project goals and architectural decisions
- **[Development Plan](docs/planning/COMPREHENSIVE_DEVELOPMENT_PLAN.md)** - Detailed roadmap and milestones
- **[Analysis Architecture](docs/architecture/ANALYSIS_SYSTEM_ARCHITECTURE.md)** - Core engine design
- **[UI Architecture](docs/architecture/UI_SYSTEM_ARCHITECTURE.md)** - Interface system design
- **[Phase 2B Summary](docs/PHASE_2B_COMPLETION_SUMMARY.md)** - Recent achievements

## 🧪 **Testing**

The project includes comprehensive testing:

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch
```

**Test Coverage**:
- ✅ Core analysis engine with real mod files
- ✅ Resource type identification and metadata extraction
- ✅ 50+ test assets covering various mod types
- 🔄 UI component tests (planned for Phase 3)

## 🎯 **Development Roadmap**

### **✅ Completed Phases**
- **Phase 1**: Foundation & Core Setup
- **Phase 2A**: Core Analysis Engine
- **Phase 2B**: Enhanced UI Development

### **🔄 Current Phase: Phase 3 - Conflict Detection**
- TGI conflict detection (identical Type-Group-Instance resources)
- Content conflict analysis (similar resource content)
- Resolution tools and recommendations
- Conflict reporting and export

### **📋 Upcoming Phases**
- **Phase 4**: Advanced Modules (Mod organizer, duplicate finder)
- **Phase 5**: Polish & Distribution (Auto-updates, installers)

## 🤝 **Contributing**

We welcome contributions! Please see our development plan and architecture documentation for guidance on how to contribute effectively.

### **Development Guidelines**
- Follow the established TypeScript and Vue.js patterns
- Maintain the modular architecture
- Add tests for new functionality
- Update documentation for significant changes

## 📄 **License**

This project is licensed under the ISC License.

## 🙏 **Acknowledgments**

- **S4TK Team**: For the excellent @s4tk/models library
- **Sims 4 Modding Community**: For inspiration and testing
- **Electron & Vue.js Teams**: For the fantastic development frameworks

---

**Built with ❤️ for the Sims 4 community**

*Simonitor aims to make mod management easier, safer, and more enjoyable for all Sims 4 players.*