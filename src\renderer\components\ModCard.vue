<template>
  <div class="mod-card" :class="{ 'mod-card--expanded': isExpanded }">
    <!-- Card Header -->
    <div class="mod-card__header" @click="toggleExpanded">
      <div class="mod-card__header-main">
        <div class="mod-card__title-section">
          <h3 class="mod-card__title">{{ getDisplayName(modData.fileName) }}</h3>
          <div class="mod-card__metadata">
            <span class="mod-card__author" v-if="modData.author">
              by {{ modData.author }}
            </span>
            <span class="mod-card__version" v-if="modData.version">
              v{{ modData.version }}
            </span>
            <span class="mod-card__confidence" v-if="modData.metadataConfidence">
              {{ modData.metadataConfidence }}% confidence
            </span>
          </div>
        </div>
        
        <div class="mod-card__quick-stats">
          <div class="mod-card__file-info">
            <span class="mod-card__file-size">{{ formatFileSize(modData.fileSize) }}</span>
            <span
              class="mod-card__file-type"
              :class="`file-type--${modData.fileExtension.slice(1)}`"
              :title="getFileTypeDescription(modData.fileExtension)"
            >
              {{ getFileTypeLabel(modData.fileExtension) }}
            </span>
          </div>
          
          <div class="mod-card__intelligence-indicators">
            <IntelligenceIndicator
              v-if="hasResourceIntelligence"
              :type="intelligenceType"
              :quality-score="modData.qualityScore"
              :risk-level="modData.riskLevel"
            />
            <div v-if="modData.qualityScore" class="quality-indicator">
              <QualityBadge
                :score="modData.qualityScore"
                size="sm"
              />
              <span class="quality-description">{{ getQualityDescription(modData.qualityScore) }}</span>
            </div>
          </div>
        </div>
      </div>
      
      <button class="mod-card__expand-button" :class="{ 'expanded': isExpanded }">
        <ChevronDownIcon class="mod-card__expand-icon" />
      </button>
    </div>
    
    <!-- Expanded Content -->
    <Transition name="expand" appear>
      <div v-if="isExpanded" class="mod-card__content">
        <!-- Mod Summary Section -->
        <div class="mod-card__section mod-card__summary">
          <h4 class="mod-card__section-title">
            <StarIcon class="mod-card__section-icon" />
            What This Mod Adds
          </h4>
          <div class="mod-summary">
            <p class="mod-summary__description">{{ getModDescription() }}</p>
            <div class="mod-summary__tags">
              <span class="mod-tag" :class="`mod-tag--${getModCategory()}`">
                {{ getModCategoryLabel() }}
              </span>
              <span v-if="modData.author" class="mod-tag mod-tag--author">
                by {{ modData.author }}
              </span>
            </div>
          </div>
        </div>

        <!-- Resource Intelligence Section -->
        <div v-if="hasResourceIntelligence" class="mod-card__section">
          <h4 class="mod-card__section-title">
            <CpuChipIcon class="mod-card__section-icon" />
            {{ intelligenceType }} Analysis
          </h4>
          
          <!-- Script Intelligence -->
          <ScriptIntelligenceDisplay
            v-if="isScript && scriptIntelligence"
            :intelligence="scriptIntelligence"
          />
          
          <!-- Package Intelligence -->
          <ResourceIntelligenceDisplay
            v-else-if="isPackage && resourceIntelligence"
            :intelligence="resourceIntelligence"
            :resource-count="modData.resourceCount"
          />
        </div>
        
        <!-- Quality Assessment Section -->
        <div v-if="qualityAssessment" class="mod-card__section">
          <h4 class="mod-card__section-title">
            <StarIcon class="mod-card__section-icon" />
            Quality Assessment
          </h4>
          <QualityAssessmentDisplay :assessment="qualityAssessment" />
        </div>
        
        <!-- Dependencies Section -->
        <div v-if="dependencies" class="mod-card__section">
          <h4 class="mod-card__section-title">
            <LinkIcon class="mod-card__section-icon" />
            Dependencies & Conflicts
          </h4>
          <DependencyDisplay :dependencies="dependencies" />
        </div>
        
        <!-- Performance Impact -->
        <div v-if="performanceData" class="mod-card__section">
          <h4 class="mod-card__section-title">
            <BoltIcon class="mod-card__section-icon" />
            Performance Impact
          </h4>
          <PerformanceDisplay :performance="performanceData" />
        </div>
        
        <!-- Raw Data (Debug Mode) -->
        <div v-if="showDebugInfo" class="mod-card__section mod-card__section--debug">
          <h4 class="mod-card__section-title">
            <CodeBracketIcon class="mod-card__section-icon" />
            Raw Analysis Data
          </h4>
          <pre class="mod-card__debug-data">{{ JSON.stringify(modData, null, 2) }}</pre>
        </div>
      </div>
    </Transition>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import {
  ChevronDownIcon,
  CpuChipIcon,
  StarIcon,
  LinkIcon,
  BoltIcon,
  CodeBracketIcon
} from '@heroicons/vue/24/outline';

import IntelligenceIndicator from './IntelligenceIndicator.vue';
import QualityBadge from './QualityBadge.vue';
import ScriptIntelligenceDisplay from './ScriptIntelligenceDisplay.vue';
import ResourceIntelligenceDisplay from './ResourceIntelligenceDisplay.vue';
import QualityAssessmentDisplay from './QualityAssessmentDisplay.vue';
import DependencyDisplay from './DependencyDisplay.vue';
import PerformanceDisplay from './PerformanceDisplay.vue';

interface ModData {
  fileName: string;
  fileExtension: string;
  fileSize: number;
  author?: string;
  version?: string;
  metadataConfidence?: number;
  qualityScore?: number;
  riskLevel?: string;
  resourceCount?: number;
  hasIntelligence: boolean;
  hasResourceIntelligence: boolean;
  intelligenceType: string;
  resourceIntelligenceData?: any;
  scriptIntelligenceData?: any;
  qualityAssessmentData?: any;
  dependencyData?: any;
  processingTime: number;
}

const props = defineProps<{
  modData: ModData;
  showDebugInfo?: boolean;
}>();

const isExpanded = ref(false);

// Computed properties
const isScript = computed(() => props.modData.fileExtension === '.ts4script');
const isPackage = computed(() => props.modData.fileExtension === '.package');

const hasResourceIntelligence = computed(() => props.modData.hasResourceIntelligence);

const intelligenceType = computed(() => {
  if (isScript.value && hasResourceIntelligence.value) return 'Script Intelligence';
  if (isPackage.value && hasResourceIntelligence.value) return 'Resource Intelligence';
  if (props.modData.hasIntelligence) return 'Basic Intelligence';
  return 'No Intelligence';
});

const scriptIntelligence = computed(() => {
  return isScript.value ? props.modData.resourceIntelligenceData : null;
});

const resourceIntelligence = computed(() => {
  return isPackage.value ? props.modData.resourceIntelligenceData : null;
});

const qualityAssessment = computed(() => props.modData.qualityAssessmentData);
const dependencies = computed(() => props.modData.dependencyData);

const performanceData = computed(() => {
  if (scriptIntelligence.value?.performance) {
    return scriptIntelligence.value.performance;
  }
  if (resourceIntelligence.value?.performance) {
    return resourceIntelligence.value.performance;
  }
  return null;
});

// Methods
const toggleExpanded = () => {
  isExpanded.value = !isExpanded.value;
};

const getQualityDescription = (score: number): string => {
  if (score >= 90) return 'Excellent - High quality, no issues detected';
  if (score >= 80) return 'Very Good - Minor optimization opportunities';
  if (score >= 70) return 'Good - Some areas for improvement';
  if (score >= 60) return 'Fair - Several issues to address';
  if (score >= 50) return 'Poor - Multiple problems detected';
  return 'Very Poor - Significant issues found';
};

const getDisplayName = (fileName: string): string => {
  // Remove file extension and clean up the name for better readability
  let name = fileName.replace(/\.(package|ts4script)$/i, '');

  // Replace underscores with spaces and capitalize words
  name = name.replace(/_/g, ' ');

  // Capitalize each word
  name = name.replace(/\b\w/g, l => l.toUpperCase());

  return name;
};

const getFileTypeLabel = (extension: string): string => {
  switch (extension.toLowerCase()) {
    case '.package':
      return 'Package';
    case '.ts4script':
      return 'Script';
    default:
      return extension;
  }
};

const getFileTypeDescription = (extension: string): string => {
  switch (extension.toLowerCase()) {
    case '.package':
      return 'Sims 4 Package File - Contains game assets like CAS items, objects, lots, or gameplay modifications';
    case '.ts4script':
      return 'Sims 4 Script File - Contains Python code that modifies game behavior and adds new functionality';
    default:
      return `${extension} file`;
  }
};

const getModDescription = (): string => {
  const fileName = props.modData.fileName.toLowerCase();

  // Analyze filename for content type
  if (fileName.includes('cas') || fileName.includes('hair') || fileName.includes('clothing') || fileName.includes('skin')) {
    return 'Adds new Create-a-Sim content like hairstyles, clothing, or accessories for your Sims.';
  }
  if (fileName.includes('build') || fileName.includes('furniture') || fileName.includes('decor') || fileName.includes('object')) {
    return 'Adds new Build/Buy mode objects, furniture, or decorative items for your lots.';
  }
  if (fileName.includes('trait') || fileName.includes('career') || fileName.includes('skill')) {
    return 'Adds new gameplay features like traits, careers, or skills to enhance your Sims\' lives.';
  }
  if (fileName.includes('lot') || fileName.includes('world') || fileName.includes('venue')) {
    return 'Adds new lots, venues, or world content for your Sims to explore.';
  }
  if (props.modData.fileExtension === '.ts4script') {
    return 'Modifies game behavior and adds new functionality through custom scripting.';
  }

  return 'Enhances your Sims 4 experience with additional content or features.';
};

const getModCategory = (): string => {
  const fileName = props.modData.fileName.toLowerCase();

  if (fileName.includes('cas') || fileName.includes('hair') || fileName.includes('clothing')) return 'cas';
  if (fileName.includes('build') || fileName.includes('furniture') || fileName.includes('object')) return 'buildbuy';
  if (fileName.includes('trait') || fileName.includes('career') || fileName.includes('skill')) return 'gameplay';
  if (fileName.includes('lot') || fileName.includes('world')) return 'world';
  if (props.modData.fileExtension === '.ts4script') return 'script';

  return 'general';
};

const getModCategoryLabel = (): string => {
  const category = getModCategory();

  switch (category) {
    case 'cas': return 'Create-a-Sim';
    case 'buildbuy': return 'Build/Buy';
    case 'gameplay': return 'Gameplay';
    case 'world': return 'World/Lots';
    case 'script': return 'Script Mod';
    default: return 'General';
  }
};

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
};
</script>

<style scoped>
.mod-card {
  background: var(--bg-elevated);
  border: 1px solid var(--border-light);
  border-radius: var(--card-radius);
  box-shadow: var(--card-shadow);
  transition: all var(--duration-200) var(--ease-out);
  overflow: hidden;
}

.mod-card:hover {
  box-shadow: var(--shadow-lg);
  border-color: var(--border-medium);
}

.mod-card--expanded {
  box-shadow: var(--shadow-xl);
}

/* Header */
.mod-card__header {
  padding: var(--card-padding);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: background-color var(--duration-150) var(--ease-out);
}

.mod-card__header:hover {
  background-color: var(--bg-secondary);
}

.mod-card__header-main {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  margin-right: var(--space-4);
}

.mod-card__title-section {
  flex: 1;
  min-width: 0;
}

.mod-card__title {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0 0 var(--space-1) 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.mod-card__metadata {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

.mod-card__author {
  font-weight: var(--font-medium);
  color: var(--text-accent);
}

.mod-card__version {
  padding: var(--space-1) var(--space-2);
  background: var(--info-bg);
  color: var(--info);
  border-radius: var(--radius-sm);
  font-weight: var(--font-medium);
}

.mod-card__confidence {
  font-size: var(--text-xs);
  color: var(--text-tertiary);
}

.mod-card__quick-stats {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.mod-card__file-info {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-size: var(--text-sm);
}

.mod-card__file-size {
  color: var(--text-secondary);
  font-family: var(--font-family-mono);
}

.mod-card__file-type {
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  font-weight: var(--font-semibold);
  text-transform: uppercase;
}

.file-type--package {
  background: var(--sims-blue-bg);
  color: var(--sims-blue);
}

.file-type--ts4script {
  background: var(--sims-purple-bg);
  color: var(--sims-purple);
}

.mod-card__intelligence-indicators {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.mod-card__expand-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  background: none;
  border-radius: var(--radius-md);
  color: var(--text-tertiary);
  cursor: pointer;
  transition: all var(--duration-150) var(--ease-out);
}

.mod-card__expand-button:hover {
  background: var(--bg-tertiary);
  color: var(--text-secondary);
}

.mod-card__expand-icon {
  width: 20px;
  height: 20px;
  transition: transform var(--duration-200) var(--ease-out);
}

.mod-card__expand-button.expanded .mod-card__expand-icon {
  transform: rotate(180deg);
}

/* Content */
.mod-card__content {
  border-top: 1px solid var(--border-light);
  padding: var(--card-padding);
  background: var(--bg-secondary);
}

.mod-card__section {
  margin-bottom: var(--space-6);
}

.mod-card__section:last-child {
  margin-bottom: 0;
}

.mod-card__section-title {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-size: var(--text-base);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0 0 var(--space-4) 0;
}

.mod-card__section-icon {
  width: 20px;
  height: 20px;
  color: var(--text-accent);
}

.mod-card__section--debug {
  border-top: 1px solid var(--border-light);
  padding-top: var(--space-6);
  margin-top: var(--space-6);
}

.mod-card__debug-data {
  background: var(--bg-tertiary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  padding: var(--space-4);
  font-family: var(--font-family-mono);
  font-size: var(--text-sm);
  color: var(--text-secondary);
  overflow-x: auto;
  max-height: 300px;
  overflow-y: auto;
}

/* Transitions */
.expand-enter-active,
.expand-leave-active {
  transition: all var(--duration-300) var(--ease-out);
  overflow: hidden;
}

.expand-enter-from,
.expand-leave-to {
  opacity: 0;
  max-height: 0;
  padding-top: 0;
  padding-bottom: 0;
}

.expand-enter-to,
.expand-leave-from {
  opacity: 1;
  max-height: 1000px;
}

/* Quality Indicator Styles */
.quality-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.quality-description {
  font-size: 0.75rem;
  color: var(--text-secondary);
  text-align: center;
  font-weight: 500;
  max-width: 120px;
  line-height: 1.2;
}

/* Mod Summary Styles */
.mod-card__summary {
  background: var(--bg-subtle);
  border-radius: var(--radius-sm);
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-md);
}

.mod-summary__description {
  color: var(--text-primary);
  font-size: 0.9rem;
  line-height: 1.4;
  margin-bottom: var(--spacing-sm);
}

.mod-summary__tags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
}

.mod-tag {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.mod-tag--cas {
  background: #e3f2fd;
  color: #1565c0;
}

.mod-tag--buildbuy {
  background: #f3e5f5;
  color: #7b1fa2;
}

.mod-tag--gameplay {
  background: #e8f5e8;
  color: #2e7d32;
}

.mod-tag--world {
  background: #fff3e0;
  color: #ef6c00;
}

.mod-tag--script {
  background: #fce4ec;
  color: #c2185b;
}

.mod-tag--general {
  background: var(--bg-muted);
  color: var(--text-secondary);
}

.mod-tag--author {
  background: var(--accent-subtle);
  color: var(--accent-primary);
}
</style>
