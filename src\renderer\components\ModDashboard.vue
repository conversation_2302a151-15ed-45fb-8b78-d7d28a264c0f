<template>
  <div class="mod-dashboard">


    <!-- Dashboard Header -->
    <header class="dashboard-header">
      <div class="dashboard-header__content">
        <div class="dashboard-title">
          <h1 class="dashboard-title__main">Simonitor</h1>
          <p class="dashboard-title__subtitle">Advanced Sims 4 Mod Management</p>
        </div>
        
        <div class="dashboard-stats">
          <div class="stat-card">
            <div class="stat-card__value">{{ totalMods }}</div>
            <div class="stat-card__label">Total Mods</div>
          </div>
          <div class="stat-card">
            <div class="stat-card__value">{{ intelligentMods }}</div>
            <div class="stat-card__label">Intelligent Analysis</div>
          </div>
          <div class="stat-card">
            <div class="stat-card__value">{{ averageQuality }}/100</div>
            <div class="stat-card__label">Avg Quality</div>
          </div>
          <div class="stat-card">
            <div class="stat-card__value">{{ formatBytes(totalSize) }}</div>
            <div class="stat-card__label">Total Size</div>
          </div>
        </div>
      </div>
    </header>
    
    <!-- Filters and Search -->
    <div class="dashboard-controls">
      <div class="search-section">
        <div class="search-input-wrapper">
          <MagnifyingGlassIcon class="search-icon" />
          <input
            v-model="searchQuery"
            type="text"
            placeholder="Search mods by name, author, or content..."
            class="search-input"
          />
          <button v-if="searchQuery" @click="clearSearch" class="search-clear">
            <XMarkIcon />
          </button>
        </div>
      </div>
      
      <div class="filter-section">
        <div class="filter-group">
          <label class="filter-label">Intelligence Type</label>
          <select v-model="selectedIntelligenceFilter" class="filter-select">
            <option value="">All Types</option>
            <option value="Script Intelligence">Script Intelligence</option>
            <option value="Resource Intelligence">Resource Intelligence</option>
            <option value="Basic Intelligence">Basic Intelligence</option>
            <option value="No Intelligence">No Intelligence</option>
          </select>
        </div>
        
        <div class="filter-group">
          <label class="filter-label">File Type</label>
          <select v-model="selectedFileTypeFilter" class="filter-select">
            <option value="">All Files</option>
            <option value=".package">Package Files</option>
            <option value=".ts4script">Script Files</option>
          </select>
        </div>
        
        <div class="filter-group">
          <label class="filter-label">Quality Range</label>
          <select v-model="selectedQualityFilter" class="filter-select">
            <option value="">All Quality</option>
            <option value="excellent">Excellent (90-100)</option>
            <option value="good">Good (70-89)</option>
            <option value="fair">Fair (50-69)</option>
            <option value="poor">Poor (0-49)</option>
          </select>
        </div>
        
        <div class="filter-group">
          <label class="filter-label">Sort By</label>
          <select v-model="selectedSortOption" class="filter-select">
            <option value="name">Name</option>
            <option value="quality">Quality Score</option>
            <option value="size">File Size</option>
            <option value="author">Author</option>
            <option value="intelligence">Intelligence Type</option>
          </select>
        </div>
        
        <button @click="clearAllFilters" class="filter-clear-btn">
          Clear Filters
        </button>
      </div>
    </div>
    
    <!-- Results Summary -->
    <div class="results-summary">
      <div class="results-summary__info">
        <span class="results-count">{{ filteredMods.length }}</span>
        <span class="results-text">of {{ totalMods }} mods</span>
        <span v-if="hasActiveFilters" class="results-filtered">(filtered)</span>
      </div>
      
      <div class="view-options">
        <button
          @click="viewMode = 'cards'"
          :class="{ active: viewMode === 'cards' }"
          class="view-option"
        >
          <Squares2X2Icon />
          Cards
        </button>
        <button
          @click="viewMode = 'list'"
          :class="{ active: viewMode === 'list' }"
          class="view-option"
        >
          <ListBulletIcon />
          List
        </button>
        <button
          @click="viewMode = 'table'"
          :class="{ active: viewMode === 'table' }"
          class="view-option"
        >
          <TableCellsIcon />
          Table
        </button>
      </div>
    </div>
    
    <!-- Debug Info -->
    <div v-if="showDebugMode" class="debug-info" style="background: #f0f0f0; padding: 10px; margin: 10px 0; border-radius: 4px;">
      <h4>Debug Info:</h4>
      <p>isLoading: {{ isLoading }}</p>
      <p>props.mods length: {{ props.mods?.length || 0 }}</p>
      <p>filteredMods length: {{ filteredMods.length }}</p>
      <p>totalMods: {{ totalMods }}</p>
      <p>viewMode: {{ viewMode }}</p>
      <p>Template condition check:</p>
      <ul>
        <li>!isLoading: {{ !isLoading }}</li>
        <li>filteredMods.length > 0: {{ filteredMods.length > 0 }}</li>
        <li>Combined: {{ !isLoading && filteredMods.length > 0 }}</li>
      </ul>
    </div>

    <!-- Always visible test -->
    <div style="background: yellow; padding: 10px; margin: 10px 0;">
      <strong>ALWAYS VISIBLE TEST:</strong> This should always show. Props mods: {{ props.mods?.length || 0 }}
      <br><strong>Raw props.mods:</strong> {{ JSON.stringify(props.mods?.slice(0, 1), null, 2) }}
    </div>

    <!-- Simple test cards -->
    <div v-if="props.mods && props.mods.length > 0" style="background: lightgreen; padding: 10px; margin: 10px 0;">
      <h3>SIMPLE TEST CARDS (should show if data exists):</h3>
      <div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 10px;">
        <div v-for="(mod, index) in props.mods.slice(0, 3)" :key="index" style="border: 1px solid #ccc; padding: 10px; background: white;">
          <h4>{{ mod.fileName || mod.filePath || 'Unknown' }}</h4>
          <p>Type: {{ mod.fileType || 'Unknown' }}</p>
          <p>Size: {{ mod.fileSize || 'Unknown' }}</p>
          <p>Author: {{ mod.author || 'Unknown' }}</p>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="isLoading" class="loading-state">
      <div class="loading-spinner"></div>
      <p class="loading-text">Analyzing mods with advanced intelligence...</p>
    </div>

    <!-- Mod Grid/List -->
    <div v-if="filteredMods.length > 0" class="mod-results">
      <!-- Card View -->
      <div v-if="viewMode === 'cards'" class="mod-grid">
        <ModCard
          v-for="mod in paginatedMods"
          :key="mod.fileName"
          :mod-data="mod"
          :show-debug-info="showDebugMode"
        />
      </div>

      <!-- List View -->
      <div v-else-if="viewMode === 'list'" class="mod-list">
        <ModListItem
          v-for="mod in paginatedMods"
          :key="mod.fileName"
          :mod-data="mod"
        />
      </div>

      <!-- Table View -->
      <div v-else class="mod-table-container">
        <ModTable
          :mods="paginatedMods"
          :sort-field="selectedSortOption"
          @sort="handleSort"
        />
      </div>

      <!-- Pagination -->
      <div v-if="totalPages > 1" class="pagination">
        <button
          @click="currentPage = Math.max(1, currentPage - 1)"
          :disabled="currentPage === 1"
          class="pagination-btn"
        >
          Previous
        </button>

        <div class="pagination-info">
          Page {{ currentPage }} of {{ totalPages }}
        </div>

        <button
          @click="currentPage = Math.min(totalPages, currentPage + 1)"
          :disabled="currentPage === totalPages"
          class="pagination-btn"
        >
          Next
        </button>
      </div>
    </div>

    <!-- Force show cards for debugging (temporary) -->
    <div v-if="showDebugMode && !isLoading && props.mods && props.mods.length > 0" class="debug-force-cards" style="border: 2px solid red; margin: 10px 0;">
      <h3 style="color: red;">DEBUG: Force showing cards</h3>
      <p>Total mods: {{ props.mods.length }}</p>
      <div class="mod-grid">
        <ModCard
          v-for="mod in props.mods.slice(0, 3)"
          :key="mod.fileName"
          :mod-data="mod"
          :show-debug-info="true"
        />
      </div>
    </div>
    
    <!-- Empty State -->
    <div v-else class="empty-state">
      <div class="empty-state__icon">
        <FolderOpenIcon />
      </div>
      <h3 class="empty-state__title">No mods found</h3>
      <p class="empty-state__description">
        {{ hasActiveFilters ?
          'Try adjusting your filters to see more results.' :
          'Start by analyzing your Sims 4 mods folder.' }}
      </p>
      <div v-if="showDebugMode" style="margin: 10px 0; padding: 10px; background: #ffe6e6; border-radius: 4px;">
        <strong>Debug - Empty State:</strong><br>
        isLoading: {{ isLoading }}<br>
        props.mods: {{ props.mods }}<br>
        filteredMods.length: {{ filteredMods.length }}<br>
        hasActiveFilters: {{ hasActiveFilters }}
      </div>
      <button v-if="hasActiveFilters" @click="clearAllFilters" class="empty-state__action">
        Clear All Filters
      </button>
    </div>

    <!-- Debug Panel -->
    <div v-if="showDebugMode" class="debug-panel">
      <h4>Debug Information</h4>
      <pre>{{ debugInfo }}</pre>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import {
  MagnifyingGlassIcon,
  XMarkIcon,
  Squares2X2Icon,
  ListBulletIcon,
  TableCellsIcon,
  FolderOpenIcon
} from '@heroicons/vue/24/outline';

import ModCard from './ModCard.vue';
import ModListItem from './ModListItem.vue';
import ModTable from './ModTable.vue';

// Props
const props = withDefaults(defineProps<{
  mods: any[];
  isLoading?: boolean;
  showDebugMode?: boolean;
}>(), {
  mods: () => [],
  isLoading: false,
  showDebugMode: false
});

// Debug: Log what props we're receiving
console.log('🎯 ModDashboard props:', props);
console.log('🎯 ModDashboard mods length:', props.mods?.length);
console.log('🎯 ModDashboard mods sample:', props.mods?.[0]);
console.log('🎯 ModDashboard mods type:', typeof props.mods);
console.log('🎯 ModDashboard mods isArray:', Array.isArray(props.mods));

// Watch for prop changes
watch(() => props.mods, (newMods, oldMods) => {
  console.log('🔄 ModDashboard mods changed:');
  console.log('  - Old length:', oldMods?.length);
  console.log('  - New length:', newMods?.length);
  console.log('  - New mods:', newMods);
  console.log('  - First mod:', newMods?.[0]);
}, { immediate: true, deep: true });

// Watch for loading state changes
watch(() => props.isLoading, (newLoading, oldLoading) => {
  console.log('🔄 ModDashboard loading state changed:', oldLoading, '->', newLoading);
}, { immediate: true });

// Watch for filtered mods changes
watch(filteredMods, (newFiltered, oldFiltered) => {
  console.log('🔄 filteredMods changed:', oldFiltered?.length, '->', newFiltered?.length);
  console.log('🔄 filteredMods sample:', newFiltered?.[0]);
}, { immediate: true });

// Reactive state
const searchQuery = ref('');
const selectedIntelligenceFilter = ref('');
const selectedFileTypeFilter = ref('');
const selectedQualityFilter = ref('');
const selectedSortOption = ref('name');
const viewMode = ref<'cards' | 'list' | 'table'>('cards');
const currentPage = ref(1);
const itemsPerPage = ref(20);

// Computed properties
const totalMods = computed(() => {
  const length = props.mods?.length || 0;
  console.log('🔢 Computing totalMods:', length, 'from props.mods:', props.mods);
  return length;
});

const intelligentMods = computed(() => {
  const count = props.mods?.filter(mod => mod.hasResourceIntelligence).length || 0;
  console.log('🧠 Computing intelligentMods:', count, 'from', props.mods?.length, 'total mods');
  return count;
});

const averageQuality = computed(() => {
  const modsWithQuality = props.mods?.filter(mod => mod.qualityScore) || [];
  if (modsWithQuality.length === 0) return 0;
  const sum = modsWithQuality.reduce((acc, mod) => acc + mod.qualityScore, 0);
  const avg = Math.round(sum / modsWithQuality.length);
  console.log('⭐ Computing averageQuality:', avg, 'from', modsWithQuality.length, 'mods with quality');
  return avg;
});

const totalSize = computed(() => {
  const size = props.mods?.reduce((acc, mod) => acc + (mod.fileSize || 0), 0) || 0;
  console.log('📦 Computing totalSize:', size, 'from', props.mods?.length, 'mods');
  return size;
});

const filteredMods = computed(() => {
  console.log('🔍 Computing filteredMods, props.mods:', props.mods);
  console.log('🔍 props.mods type:', typeof props.mods);
  console.log('🔍 props.mods isArray:', Array.isArray(props.mods));
  console.log('🔍 props.mods length:', props.mods?.length);

  if (!props.mods || !Array.isArray(props.mods)) {
    console.log('🔍 filteredMods returning empty array - no valid props.mods');
    return [];
  }

  let filtered = [...props.mods];
  console.log('🔍 Starting with filtered length:', filtered.length);

  // Search filter
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    filtered = filtered.filter(mod =>
      mod.fileName.toLowerCase().includes(query) ||
      (mod.author && mod.author.toLowerCase().includes(query)) ||
      (mod.modName && mod.modName.toLowerCase().includes(query))
    );
  }

  // Intelligence type filter
  if (selectedIntelligenceFilter.value) {
    filtered = filtered.filter(mod =>
      mod.intelligenceType === selectedIntelligenceFilter.value
    );
  }

  // File type filter
  if (selectedFileTypeFilter.value) {
    filtered = filtered.filter(mod =>
      mod.fileExtension === selectedFileTypeFilter.value
    );
  }

  // Quality filter
  if (selectedQualityFilter.value) {
    filtered = filtered.filter(mod => {
      if (!mod.qualityScore) return false;
      const score = mod.qualityScore;
      switch (selectedQualityFilter.value) {
        case 'excellent': return score >= 90;
        case 'good': return score >= 70 && score < 90;
        case 'fair': return score >= 50 && score < 70;
        case 'poor': return score < 50;
        default: return true;
      }
    });
  }

  // Sort
  filtered.sort((a, b) => {
    switch (selectedSortOption.value) {
      case 'name':
        return a.fileName.localeCompare(b.fileName);
      case 'quality':
        return (b.qualityScore || 0) - (a.qualityScore || 0);
      case 'size':
        return (b.fileSize || 0) - (a.fileSize || 0);
      case 'author':
        return (a.author || '').localeCompare(b.author || '');
      case 'intelligence':
        return a.intelligenceType.localeCompare(b.intelligenceType);
      default:
        return 0;
    }
  });

  console.log('🔍 filteredMods final result length:', filtered.length);
  console.log('🔍 filteredMods first item:', filtered[0]);
  return filtered;
});

const totalPages = computed(() => 
  Math.ceil(filteredMods.value.length / itemsPerPage.value)
);

const paginatedMods = computed(() => {
  const start = (currentPage.value - 1) * itemsPerPage.value;
  const end = start + itemsPerPage.value;
  const paginated = filteredMods.value.slice(start, end);
  console.log('📄 paginatedMods:', paginated.length, 'mods (page', currentPage.value, 'of', Math.ceil(filteredMods.value.length / itemsPerPage.value), ')');
  return paginated;
});

const hasActiveFilters = computed(() => 
  searchQuery.value || 
  selectedIntelligenceFilter.value || 
  selectedFileTypeFilter.value || 
  selectedQualityFilter.value
);

const debugInfo = computed(() => ({
  totalMods: totalMods.value,
  filteredMods: filteredMods.value.length,
  currentFilters: {
    search: searchQuery.value,
    intelligence: selectedIntelligenceFilter.value,
    fileType: selectedFileTypeFilter.value,
    quality: selectedQualityFilter.value
  },
  viewMode: viewMode.value,
  currentPage: currentPage.value
}));

// Methods
const clearSearch = () => {
  searchQuery.value = '';
};

const clearAllFilters = () => {
  searchQuery.value = '';
  selectedIntelligenceFilter.value = '';
  selectedFileTypeFilter.value = '';
  selectedQualityFilter.value = '';
  currentPage.value = 1;
};

const handleSort = (field: string) => {
  selectedSortOption.value = field;
  currentPage.value = 1;
};

const formatBytes = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
};

// Watch for filter changes to reset pagination
const resetPagination = () => {
  currentPage.value = 1;
};

// Lifecycle
onMounted(() => {
  // Any initialization logic
});
</script>

<style scoped>
.mod-dashboard {
  min-height: 100vh;
  background: var(--bg-secondary);
}

/* Header */
.dashboard-header {
  background: var(--bg-primary);
  border-bottom: 1px solid var(--border-light);
  padding: var(--space-6) 0;
}

.dashboard-header__content {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 var(--space-6);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dashboard-title__main {
  font-size: var(--text-3xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin: 0;
  background: linear-gradient(135deg, var(--sims-blue), var(--plumbob-green));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.dashboard-title__subtitle {
  font-size: var(--text-base);
  color: var(--text-secondary);
  margin: var(--space-1) 0 0 0;
}

.dashboard-stats {
  display: flex;
  gap: var(--space-6);
}

.stat-card {
  text-align: center;
  padding: var(--space-4);
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-light);
  min-width: 100px;
}

.stat-card__value {
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  font-family: var(--font-family-mono);
}

.stat-card__label {
  font-size: var(--text-xs);
  color: var(--text-tertiary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-top: var(--space-1);
}

/* Controls */
.dashboard-controls {
  max-width: 1280px;
  margin: 0 auto;
  padding: var(--space-6);
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.search-section {
  display: flex;
  justify-content: center;
}

.search-input-wrapper {
  position: relative;
  width: 100%;
  max-width: 600px;
}

.search-icon {
  position: absolute;
  left: var(--space-3);
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  color: var(--text-tertiary);
}

.search-input {
  width: 100%;
  height: 48px;
  padding: 0 var(--space-12) 0 var(--space-10);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-xl);
  background: var(--bg-primary);
  font-size: var(--text-base);
  color: var(--text-primary);
  transition: all var(--duration-200) var(--ease-out);
}

.search-input:focus {
  outline: none;
  border-color: var(--sims-blue);
  box-shadow: 0 0 0 3px var(--sims-blue-bg);
}

.search-clear {
  position: absolute;
  right: var(--space-3);
  top: 50%;
  transform: translateY(-50%);
  width: 24px;
  height: 24px;
  border: none;
  background: none;
  color: var(--text-tertiary);
  cursor: pointer;
  border-radius: var(--radius-sm);
  display: flex;
  align-items: center;
  justify-content: center;
}

.search-clear:hover {
  background: var(--bg-tertiary);
  color: var(--text-secondary);
}

.filter-section {
  display: flex;
  gap: var(--space-4);
  align-items: end;
  flex-wrap: wrap;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.filter-label {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-secondary);
}

.filter-select {
  padding: var(--space-2) var(--space-3);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  background: var(--bg-primary);
  font-size: var(--text-sm);
  color: var(--text-primary);
  min-width: 150px;
}

.filter-clear-btn {
  padding: var(--space-2) var(--space-4);
  border: 1px solid var(--border-medium);
  border-radius: var(--radius-md);
  background: var(--bg-primary);
  color: var(--text-secondary);
  font-size: var(--text-sm);
  cursor: pointer;
  transition: all var(--duration-150) var(--ease-out);
}

.filter-clear-btn:hover {
  background: var(--bg-secondary);
  border-color: var(--border-strong);
}

/* Results */
.results-summary {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 var(--space-6) var(--space-4) var(--space-6);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.results-summary__info {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-size: var(--text-sm);
}

.results-count {
  font-weight: var(--font-bold);
  color: var(--text-primary);
  font-family: var(--font-family-mono);
}

.results-text {
  color: var(--text-secondary);
}

.results-filtered {
  color: var(--text-accent);
  font-weight: var(--font-medium);
}

.view-options {
  display: flex;
  gap: var(--space-1);
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  padding: var(--space-1);
}

.view-option {
  display: flex;
  align-items: center;
  gap: var(--space-1);
  padding: var(--space-2) var(--space-3);
  border: none;
  background: none;
  color: var(--text-secondary);
  font-size: var(--text-sm);
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: all var(--duration-150) var(--ease-out);
}

.view-option:hover {
  background: var(--bg-secondary);
  color: var(--text-primary);
}

.view-option.active {
  background: var(--sims-blue);
  color: var(--text-inverse);
}

.view-option svg {
  width: 16px;
  height: 16px;
}

/* Content */
.mod-results {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 var(--space-6) var(--space-6) var(--space-6);
}

.mod-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: var(--space-6);
}

.mod-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.mod-table-container {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-light);
  overflow: hidden;
}

/* Loading */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-16);
  text-align: center;
}

.loading-spinner {
  width: 48px;
  height: 48px;
  border: 4px solid var(--border-light);
  border-top: 4px solid var(--sims-blue);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--space-4);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: var(--text-lg);
  color: var(--text-secondary);
  margin: 0;
}

/* Empty State */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-16);
  text-align: center;
}

.empty-state__icon {
  width: 64px;
  height: 64px;
  color: var(--text-tertiary);
  margin-bottom: var(--space-4);
}

.empty-state__title {
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0 0 var(--space-2) 0;
}

.empty-state__description {
  font-size: var(--text-base);
  color: var(--text-secondary);
  margin: 0 0 var(--space-6) 0;
  max-width: 400px;
}

.empty-state__action {
  padding: var(--space-3) var(--space-6);
  background: var(--sims-blue);
  color: var(--text-inverse);
  border: none;
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  cursor: pointer;
  transition: all var(--duration-150) var(--ease-out);
}

.empty-state__action:hover {
  background: var(--sims-blue-dark);
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: var(--space-4);
  margin-top: var(--space-8);
}

.pagination-btn {
  padding: var(--space-2) var(--space-4);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  background: var(--bg-primary);
  color: var(--text-primary);
  font-size: var(--text-sm);
  cursor: pointer;
  transition: all var(--duration-150) var(--ease-out);
}

.pagination-btn:hover:not(:disabled) {
  background: var(--bg-secondary);
  border-color: var(--border-medium);
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-info {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  font-family: var(--font-family-mono);
}
</style>
