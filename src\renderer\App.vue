<template>
  <div class="app">
    <!-- Enhanced Header -->
    <header class="app-header">
      <div class="container">
        <div class="flex items-center justify-between py-lg">
          <div class="app-title">
            <h1 class="text-3xl font-bold">Simonitor</h1>
            <p class="text-muted">Advanced Sims 4 Mod Management & Intelligence</p>
          </div>
          <div class="app-actions flex items-center gap-sm">
            <button
              class="btn btn-primary btn-sm"
              @click="selectAndAnalyzeFolder"
              :disabled="isAnalyzing"
            >
              <FolderOpenIcon class="w-4 h-4" />
              Analyze Mods Folder
            </button>
            <button
              class="btn btn-secondary btn-sm"
              @click="loadTestData"
              style="margin-left: 8px;"
            >
              Load Test Data
            </button>
            <button
              v-if="analysisResults.length > 0"
              class="btn btn-secondary btn-sm"
              @click="exportResults"
            >
              <ArrowDownTrayIcon class="w-4 h-4" />
              Export
            </button>
            <button class="btn btn-secondary btn-sm" @click="openSettings">
              <CogIcon class="w-4 h-4" />
              Settings
            </button>
            <span class="badge badge-info">v2.0.0</span>
          </div>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="app-main">


      <!-- Enhanced ModDashboard Interface -->
      <ModDashboard
        :mods="analysisResults"
        :is-loading="isAnalyzing"
        :show-debug-mode="showDebugMode"
      />

      <!-- Error Display -->
      <div v-if="analysisError" class="container">
        <section class="error-section mb-xl">
          <div class="card">
            <div class="error-content">
              <div class="flex items-center gap-md mb-md">
                <ExclamationTriangleIcon class="w-6 h-6 text-error" />
                <h3 class="font-medium text-error">Analysis Error</h3>
              </div>
              <p class="text-muted mb-md">{{ analysisError }}</p>
              <button class="btn btn-secondary btn-sm" @click="clearError">
                Dismiss
              </button>
            </div>
          </div>
        </section>
      </div>
    </main>

    <!-- Footer -->
    <footer class="app-footer">
      <div class="container">
        <div class="text-center py-md text-sm text-muted">
          <p>Simonitor - Built with ❤️ for the Sims 4 community</p>
        </div>
      </div>
    </footer>

    <!-- Settings Modal -->
    <Modal
      :is-open="isSettingsOpen"
      title="Settings"
      @close="closeSettings"
    >
      <AppSettings
        @settings-changed="handleSettingsChanged"
        ref="settingsRef"
      />
    </Modal>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, nextTick } from 'vue';
import {
  FolderOpenIcon,
  ArrowDownTrayIcon,
  CogIcon,
  ExclamationTriangleIcon
} from '@heroicons/vue/24/outline';

import ModDashboard from './components/ModDashboard.vue';
import FileUpload from './components/FileUpload.vue';
import Modal from './components/Modal.vue';
import AppSettings from './components/AppSettings.vue';

// Types
import type { AnalyzedPackage, ResourceInfo } from '../types/analysis';

// Reactive state
const selectedFiles = ref<File[]>([]);
const analysisResults = ref<any[]>([]);
const isAnalyzing = ref(false);

// Test data for debugging
const testMods = ref([
  {
    fileName: 'Test Mod 1.package',
    fileType: 'Package',
    fileSize: 1024000,
    author: 'Test Author',
    version: '1.0',
    modName: 'Test Mod 1',
    hasResourceIntelligence: true,
    intelligenceType: 'Resource Intelligence',
    qualityScore: 85,
    riskLevel: 'low',
    fileExtension: '.package'
  },
  {
    fileName: 'Test Mod 2.ts4script',
    fileType: 'Script',
    fileSize: 512000,
    author: 'Another Author',
    version: '2.1',
    modName: 'Test Mod 2',
    hasResourceIntelligence: true,
    intelligenceType: 'Script Intelligence',
    qualityScore: 92,
    riskLevel: 'medium',
    fileExtension: '.ts4script'
  }
]);
const analyzedCount = ref(0);
const analysisError = ref<string | null>(null);
const fileUploadRef = ref();
const isSettingsOpen = ref(false);
const settingsRef = ref();
const showDebugMode = ref(true); // Temporarily enabled for debugging
const currentModsFolder = ref<string>('');

// Watch for analysis results changes
watch(analysisResults, (newResults, oldResults) => {
  console.log('🔄 App.vue analysisResults changed:', oldResults?.length, '->', newResults?.length);
  console.log('🔄 App.vue first result:', newResults?.[0]);
}, { immediate: true, deep: true });

// Watch for isAnalyzing changes
watch(isAnalyzing, (newAnalyzing, oldAnalyzing) => {
  console.log('🔄 App.vue isAnalyzing changed:', oldAnalyzing, '->', newAnalyzing);
}, { immediate: true });

// Event handlers
function handleFilesSelected(files: File[]) {
  selectedFiles.value = files;
  // Clear previous results when new files are selected
  if (files.length === 0) {
    analysisResults.value = [];
    analysisError.value = null;
  }
}

async function handleAnalyzeRequested(files: File[]) {
  if (files.length === 0) return;

  if (!window.electronAPI) {
    analysisError.value = 'Electron API not available. Please run in Electron app.';
    return;
  }

  isAnalyzing.value = true;
  analyzedCount.value = 0;
  analysisResults.value = [];
  analysisError.value = null;

  try {
    // Analyze files one by one
    for (const file of files) {
      const filePath = (file as any).path;
      
      // Create a promise to handle the async analysis
      const analysisPromise = new Promise<AnalyzedPackage>((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('Analysis timeout'));
        }, 30000); // 30 second timeout
        
        const handleResult = (result: any) => {
          clearTimeout(timeout);
          window.electronAPI.offAnalysisResult?.(handleResult);
          
          if (result.error) {
            reject(new Error(result.error));
          } else {
            resolve(result);
          }
        };
        
        window.electronAPI.onAnalysisResult(handleResult);
        window.electronAPI.analyzePackage(filePath);
      });
      
      try {
        const result = await analysisPromise;
        analysisResults.value.push(result);
        analyzedCount.value++;
      } catch (error) {
        console.error(`Error analyzing ${file.name}:`, error);
        analysisError.value = `Error analyzing ${file.name}: ${error instanceof Error ? error.message : 'Unknown error'}`;
        break;
      }
    }
  } catch (error) {
    analysisError.value = error instanceof Error ? error.message : 'An unexpected error occurred';
  } finally {
    isAnalyzing.value = false;
  }
}

function clearError() {
  analysisError.value = null;
}

function loadTestData() {
  isAnalyzing.value = false;
  analysisResults.value = [...testMods.value];
}

// Settings handlers
function openSettings() {
  isSettingsOpen.value = true;
}

function closeSettings() {
  isSettingsOpen.value = false;
}

function handleSettingsChanged(settings: any) {
  // Handle settings changes - could be used to update app behavior
  console.log('Settings updated:', settings);
  if (settings.debugMode !== undefined) {
    showDebugMode.value = settings.debugMode;
  }
}

// New enhanced methods for folder analysis
async function selectAndAnalyzeFolder() {
  try {
    if (!window.electronAPI) {
      analysisError.value = 'Electron API not available. Please run in Electron app.';
      return;
    }

    isAnalyzing.value = true;
    analysisError.value = null;

    // Open folder selection dialog
    const folderResult = await window.electronAPI.selectModsFolder();

    if (!folderResult.success) {
      if (folderResult.error !== 'No folder selected') {
        analysisError.value = folderResult.error;
      }
      return;
    }

    currentModsFolder.value = folderResult.path;
    console.log('Analyzing mods folder:', folderResult.path);

    // Analyze the entire folder
    const analysisResult = await window.electronAPI.analyzeModsFolder(folderResult.path);

    if (analysisResult.success) {
      console.log('Raw analysis result:', analysisResult);
      console.log('Raw analysis data:', analysisResult.data);
      console.log('Data type:', typeof analysisResult.data);
      console.log('Is array:', Array.isArray(analysisResult.data));

      // Check if data exists and is an array
      if (analysisResult.data && Array.isArray(analysisResult.data)) {
        analysisResults.value = analysisResult.data.map((result: any) => ({
          ...result,
          // Map filePath to fileName for UI compatibility
          fileName: result.filePath ? getFileName(result.filePath) : result.fileName || 'Unknown',
          // Ensure all required properties for the UI
          hasResourceIntelligence: !!result.intelligence?.resourceIntelligence,
          intelligenceType: getIntelligenceType(result),
          qualityScore: result.intelligence?.qualityAssessment?.overallScore || 0,
          riskLevel: result.intelligence?.resourceIntelligence?.riskLevel || 'unknown',
          resourceIntelligenceData: result.intelligence?.resourceIntelligence,
          scriptIntelligenceData: result.intelligence?.resourceIntelligence,
          qualityAssessmentData: result.intelligence?.qualityAssessment,
          dependencyData: result.intelligence?.dependencies,
          metadataConfidence: result.metadataConfidence || 0,
          processingTime: result.processingTime || 0,
          // Ensure metadata properties exist
          author: result.metadata?.author || result.author || 'Unknown',
          version: result.metadata?.version || result.version || 'Unknown',
          modName: result.metadata?.modName || result.modName || result.fileName || 'Unknown'
        }));

        console.log(`Successfully analyzed ${analysisResults.value.length} mods`);
        console.log('🔍 Raw analysis result sample:', analysisResult.data[0]);
        console.log('🔍 Mapped analysis result sample:', analysisResults.value[0]);
        console.log('🔍 Sample mod properties:', Object.keys(analysisResults.value[0] || {}));
        console.log('🔍 fileName mapping check:', {
          original: analysisResult.data[0]?.filePath,
          mapped: analysisResults.value[0]?.fileName
        });
        console.log('Full analysis results:', analysisResults.value);
        console.log('🔧 ANALYSIS COMPLETE: About to exit try block, isAnalyzing is:', isAnalyzing.value);

        // Force reactivity update
        console.log('🔧 Forcing reactivity update...');
        analysisResults.value = [...analysisResults.value];

        // Force Vue to re-render
        await nextTick();
        console.log('🔧 After nextTick, analysisResults.value.length:', analysisResults.value.length);
      } else {
        console.error('Analysis data is not an array:', analysisResult.data);
        analysisError.value = 'Invalid analysis data format received';
      }
    } else {
      analysisError.value = analysisResult.error;
    }
  } catch (error) {
    console.error('Folder analysis error:', error);
    analysisError.value = error instanceof Error ? error.message : 'Failed to analyze mods folder';
  } finally {
    console.log('🔧 FINALLY BLOCK: Setting isAnalyzing to false');
    isAnalyzing.value = false;
    console.log('🔧 FINALLY BLOCK: isAnalyzing is now:', isAnalyzing.value);

    // Force another nextTick to ensure UI updates
    await nextTick();
    console.log('🔧 FINALLY BLOCK: After final nextTick');
  }
}

async function exportResults() {
  if (analysisResults.value.length === 0) return;

  if (!window.electronAPI) {
    analysisError.value = 'Electron API not available. Please run in Electron app.';
    return;
  }

  try {
    // For now, export as JSON - could add format selection dialog
    const exportResult = await window.electronAPI.exportResults(analysisResults.value, 'json');

    if (exportResult.success) {
      console.log('Results exported to:', exportResult.path);
      // Could show a success notification here
    } else {
      analysisError.value = exportResult.error;
    }
  } catch (error) {
    console.error('Export error:', error);
    analysisError.value = error instanceof Error ? error.message : 'Failed to export results';
  }
}

// Helper function to get filename from path (replaces path.basename)
function getFileName(filePath: string): string {
  if (!filePath) return 'Unknown';
  return filePath.split(/[\\/]/).pop() || 'Unknown';
}

function getIntelligenceType(result: any): string {
  if (result.fileExtension === '.ts4script' && result.intelligence?.resourceIntelligence) {
    return 'Script Intelligence';
  }
  if (result.fileExtension === '.package' && result.intelligence?.resourceIntelligence) {
    return 'Resource Intelligence';
  }
  if (result.intelligence) {
    return 'Basic Intelligence';
  }
  return 'No Intelligence';
}

// Auto-load default mods folder on startup
onMounted(async () => {
  console.log('Simonitor loaded and ready');
  // No auto-analysis - user must manually select and analyze folder
});

// Set up the analysis result handler (legacy support)
if (window.electronAPI?.onAnalysisResult) {
  window.electronAPI.onAnalysisResult((result: any) => {
    // This will be handled by the promise in handleAnalyzeRequested
  });
}
</script>

<style>
/* Import our enhanced design system */
@import './styles/simonitor-design-system.css';

.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: var(--bg-secondary);
  font-family: var(--font-family-sans);
}

.app-header {
  background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
  border-bottom: 1px solid var(--border-light);
  box-shadow: var(--shadow-md);
  backdrop-filter: blur(10px);
}

.app-title h1 {
  background: linear-gradient(135deg, var(--sims-blue), var(--plumbob-green));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: var(--font-bold);
}

.app-main {
  flex: 1;
}

.app-footer {
  background-color: var(--bg-primary);
  border-top: 1px solid var(--border-light);
  margin-top: auto;
}

/* Button styles */
.btn {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-4);
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  transition: all var(--duration-150) var(--ease-out);
  border: 1px solid transparent;
  cursor: pointer;
  text-decoration: none;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background: var(--sims-blue);
  color: var(--text-inverse);
  border-color: var(--sims-blue);
}

.btn-primary:hover:not(:disabled) {
  background: var(--sims-blue-dark);
  border-color: var(--sims-blue-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background: var(--bg-primary);
  color: var(--text-primary);
  border-color: var(--border-medium);
}

.btn-secondary:hover:not(:disabled) {
  background: var(--bg-secondary);
  border-color: var(--border-strong);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.btn-sm {
  padding: var(--space-1) var(--space-3);
  font-size: var(--text-xs);
}

/* Badge styles */
.badge {
  display: inline-flex;
  align-items: center;
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: var(--font-semibold);
}

.badge-info {
  background: var(--info-bg);
  color: var(--info);
  border: 1px solid var(--info-border);
}

/* Card styles */
.card {
  background: var(--bg-elevated);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  overflow: hidden;
}

/* Container */
.container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 var(--space-6);
}

/* Utility classes */
.w-4 { width: 16px; }
.h-4 { height: 16px; }
.w-6 { width: 24px; }
.h-6 { height: 24px; }

.text-error {
  color: var(--error);
}

.text-muted {
  color: var(--text-secondary);
}

.welcome-content {
  max-width: 600px;
  margin: 0 auto;
}

.welcome-icon {
  color: var(--text-muted);
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-lg);
  margin-top: var(--spacing-xl);
}

.feature-item {
  text-align: center;
  padding: var(--spacing-lg);
}

.feature-icon {
  color: var(--primary-color);
  margin-bottom: var(--spacing-md);
}

.feature-item h4 {
  margin: 0 0 var(--spacing-sm) 0;
}

.feature-item p {
  margin: 0;
}

.max-w-md {
  max-width: 28rem;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.text-error {
  color: var(--error-color);
}

.loading-content .loading {
  border-width: 3px;
}

@media (max-width: 768px) {
  .app-title h1 {
    font-size: 1.5rem;
  }
  
  .features-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
  
  .feature-item {
    padding: var(--spacing-md);
  }
}
</style>